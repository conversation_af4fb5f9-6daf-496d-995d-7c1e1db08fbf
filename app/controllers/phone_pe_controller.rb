class PhonePeController < ExternalServiceController
  before_action :log_request
  after_action :log_response

  def log_request
    Rails.logger.warn("#{controller_name}#{action_name} :: PARAMS: #{params}")
  end

  def log_response
    Rails.logger.warn("#{controller_name}#{action_name} :: RESPONSE: #{response.body} :: PARAMS: #{params}")
  end

  # Handle PhonePe subscription callbacks
  # This method processes various subscription-related events from PhonePe
  # For subscription charges, we first try to find them by pg_id (using orderId or merchantOrderId)
  # and only fall back to the last subscription charge if no pg_id is available
  def subscription_callback
    # Process the callback based on the event type
    case params[:event]
    when "subscription.setup.order.completed"
      handle_subscription_setup_completed
    when "subscription.setup.order.failed"
      handle_subscription_setup_failed
    when "subscription.notification.completed", "subscription.notification.failed"
      handle_subscription_notification
    when "subscription.redemption.transaction.completed", "subscription.redemption.transaction.failed"
      handle_subscription_redemption_transaction
    when "subscription.cancelled"
      handle_subscription_cancelled
    when "subscription.revoked"
      handle_subscription_revoked
    when "subscription.paused"
      handle_subscription_paused
    when "pg.refund.completed", "pg.refund.failed"
      handle_refund_callback
    else
      # Handle other subscription events
      Rails.logger.warn("Unhandled PhonePe subscription event: #{params[:event]},
                          payload: #{params[:payload]}")
    end

    # Always return success to PhonePe
    render json: { status: "OK" }, status: :ok
  end

  private

  def handle_subscription_setup_completed
    payload = params[:payload]
    merchant_subscription_id = payload.dig(:paymentFlow, :merchantSubscriptionId)

    # Find the subscription by pg_id
    subscription = Subscription.find_by(pg_id: merchant_subscription_id)

    if subscription.present?
      # Update subscription with PhonePe's subscription ID
      phonepe_subscription_id = payload.dig(:paymentFlow, :subscriptionId)
      subscription.pg_json = payload
      subscription.pg_reference_id = phonepe_subscription_id
      subscription.save!

      # Find the subscription charge by merchant order ID
      subscription_charge = nil
      if payload.dig(:merchantOrderId).present?
        subscription_charge = SubscriptionCharge.find_by(pg_id: payload[:merchantOrderId])
      end

      if subscription_charge.present?
        # Update the subscription charge with the payload
        subscription_charge.pg_json = payload
        subscription_charge.save!

        # Check if the state is COMPLETED
        if payload[:state] == "COMPLETED"
          # Update the subscription charge status
          # This will automatically activate the subscription via after_success callback
          if subscription_charge.may_success?
            subscription_charge.success!
            Rails.logger.warn("PhonePe subscription charge marked as success for charge ID: #{subscription_charge.id}")
          else
            Honeybadger.notify('Invalid state for subscription_charge in PhonePe setup completed',
                               context: { subscription_charge_id: subscription_charge.id, current_state: subscription_charge.status, params: params })
          end
        else
          # If the state is not COMPLETED, log a notification
          Honeybadger.notify('Unexpected state for subscription_charge in PhonePe setup completed',
                             context: { subscription_charge_id: subscription_charge.id, state: payload[:state], params: params })
        end
      else
        Honeybadger.notify('Subscription charge not found for PhonePe setup completed',
                           context: { subscription_id: subscription.id, merchant_subscription_id: merchant_subscription_id, params: params })
      end

      Rails.logger.warn("PhonePe subscription setup completed for subscription ID: #{subscription.id}")
    else
      Rails.logger.error("PhonePe subscription not found for merchant_subscription_id: #{merchant_subscription_id}")
      Honeybadger.notify('PhonePe subscription not found for setup completed',
                         context: { merchant_subscription_id: merchant_subscription_id, params: params })
    end
  end

  def handle_subscription_setup_failed
    payload = params[:payload]
    merchant_subscription_id = payload.dig(:paymentFlow, :merchantSubscriptionId)

    # Find the subscription by pg_id
    subscription = Subscription.find_by(pg_id: merchant_subscription_id)

    if subscription.present?
      # Update subscription with payload
      subscription.pg_json = payload
      subscription.save!

      # Find the subscription charge by merchant order ID
      subscription_charge = nil
      if payload.dig(:merchantOrderId).present?
        subscription_charge = SubscriptionCharge.find_by(pg_id: payload[:merchantOrderId])
      end

      if subscription_charge.present?
        # Update the subscription charge with the payload
        subscription_charge.pg_json = payload
        subscription_charge.save!

        # Check if the state is FAILED
        if payload[:state] == "FAILED"
          # Mark the charge as failed if possible
          if subscription_charge.may_fail?
            subscription_charge.fail!
            Rails.logger.warn("PhonePe subscription charge marked as failed for charge ID: #{subscription_charge.id}")
          else
            Honeybadger.notify('Invalid state for subscription_charge in PhonePe setup failed',
                               context: { subscription_charge_id: subscription_charge.id, current_state: subscription_charge.status, params: params })
          end
        else
          # If the state is not FAILED, log a notification
          Honeybadger.notify('Unexpected state for subscription_charge in PhonePe setup failed',
                             context: { subscription_charge_id: subscription_charge.id, state: payload[:state], params: params })
        end
      else
        Honeybadger.notify('Subscription charge not found for PhonePe setup failed',
                           context: { subscription_id: subscription.id, merchant_subscription_id: merchant_subscription_id, params: params })
      end

      # Close the subscription
      if subscription.may_close?
        subscription.close!
        Rails.logger.warn("PhonePe subscription closed for subscription ID: #{subscription.id}")
      elsif !subscription.closed?
        Honeybadger.notify('Invalid state for subscription in PhonePe setup failed',
                           context: { subscription_id: subscription.id, current_state: subscription.status, params: params })
      end

      Rails.logger.warn("PhonePe subscription setup failed for subscription ID: #{subscription.id}")
    else
      Rails.logger.error("PhonePe subscription not found for merchant_subscription_id: #{merchant_subscription_id}")
      Honeybadger.notify('PhonePe subscription not found for setup failed',
                         context: { merchant_subscription_id: merchant_subscription_id, params: params })
    end
  end

  def handle_subscription_notification
    # Handle notification events (when PhonePe notifies the user about upcoming charge)
    payload = params[:payload]
    merchant_subscription_id = payload.dig(:paymentFlow, :merchantSubscriptionId)
    merchant_order_id = payload[:merchantOrderId]

    # Find the subscription by pg_id
    subscription = Subscription.find_by(pg_id: merchant_subscription_id)

    if subscription.present?
      # Check if this notification has autoDebit set to true
      auto_debit = payload.dig(:paymentFlow, :autoDebit)

      # Log the notification event with additional details
      Rails.logger.warn("PhonePe subscription notification received for subscription ID: #{subscription.id}, event: #{params[:event]}, autoDebit: #{auto_debit}")

      # If there's a merchant order ID, try to find or create a subscription charge
      if merchant_order_id.present?
        subscription_charge = SubscriptionCharge.find_by(pg_id: merchant_order_id)

        if subscription_charge.present?
          # Update the existing charge with the notification payload
          subscription_charge.pg_json = payload
          subscription_charge.save!
          Rails.logger.warn("Updated existing subscription charge ID: #{subscription_charge.id} with notification data")
        else
          Rails.logger.warn("No subscription charge found for merchant_order_id: #{merchant_order_id} in notification")
        end
      end
    else
      Rails.logger.error("PhonePe subscription not found for merchant_subscription_id: #{merchant_subscription_id}")
      Honeybadger.notify('PhonePe subscription not found for notification',
                         context: { merchant_subscription_id: merchant_subscription_id, params: params })
    end
  end

  def handle_subscription_redemption_transaction
    # Handle redemption transaction events (when a charge is completed or failed)
    payload = params[:payload]
    merchant_subscription_id = payload.dig(:paymentFlow, :merchantSubscriptionId)
    merchant_order_id = payload[:merchantOrderId]
    order_id = payload[:orderId]
    auto_debit = payload.dig(:paymentFlow, :autoDebit)

    # Find the subscription by pg_id
    subscription = Subscription.find_by(pg_id: merchant_subscription_id)

    if subscription.present?
      # Find the subscription charge by pg_id
      subscription_charge = SubscriptionCharge.find_by(pg_id: merchant_order_id)

      # Check if this is an auto debit transaction
      Rails.logger.warn("PhonePe redemption transaction for subscription ID: #{subscription.id}, autoDebit: #{auto_debit}")

      # If autoDebit is true, we might have already processed this charge in the order event
      # This is similar to how Juspay handles auto debits
      if auto_debit && subscription_charge.present? && subscription_charge.success?
        Rails.logger.warn("PhonePe auto debit charge already processed for charge ID: #{subscription_charge.id}")
        return
      end

      if subscription_charge.present?
        # Update the subscription charge with the transaction payload
        subscription_charge.pg_json = payload
        subscription_charge.pg_reference_id = order_id if order_id.present?
        subscription_charge.save!

        # Update the status based on the event and state
        if params[:event] == "subscription.redemption.transaction.completed" && payload[:state] == "COMPLETED"
          # Mark as success if it's in created or sent_to_pg state
          if subscription_charge.may_success?
            subscription_charge.success!
            Rails.logger.warn("PhonePe subscription charge marked as success for charge ID: #{subscription_charge.id}")
          else
            Honeybadger.notify('Invalid state for subscription_charge in PhonePe redemption transaction completed',
                               context: { subscription_charge_id: subscription_charge.id, current_state: subscription_charge.status, params: params })
          end
        elsif params[:event] == "subscription.redemption.transaction.failed" && payload[:state] == "FAILED"
          # Mark as failed if it's in created or sent_to_pg state
          if subscription_charge.may_fail?
            subscription_charge.fail!
            Rails.logger.warn("PhonePe subscription charge marked as failed for charge ID: #{subscription_charge.id}")
          else
            Honeybadger.notify('Invalid state for subscription_charge in PhonePe redemption transaction failed',
                               context: { subscription_charge_id: subscription_charge.id, current_state: subscription_charge.status, params: params })
          end
        else
          # Log unexpected event/state combinations
          Honeybadger.notify('Unexpected event/state for subscription_charge in PhonePe redemption transaction',
                             context: { subscription_charge_id: subscription_charge.id, event: params[:event], state: payload[:state], params: params })
        end

        Rails.logger.warn("PhonePe subscription redemption transaction #{params[:event]} for subscription ID: #{subscription.id}, charge ID: #{subscription_charge.id}")
      else
        # If the subscription charge is not found, log a notification
        Rails.logger.warn("No subscription charge found for merchant_order_id: #{merchant_order_id} in PhonePe redemption transaction")
        Honeybadger.notify('Subscription charge not found for PhonePe redemption transaction',
                           context: { subscription_id: subscription.id, merchant_subscription_id: merchant_subscription_id, params: params })
      end
    else
      Rails.logger.error("PhonePe subscription not found for merchant_subscription_id: #{merchant_subscription_id}")
      Honeybadger.notify('PhonePe subscription not found for redemption transaction',
                         context: { merchant_subscription_id: merchant_subscription_id, params: params })
    end
  end

  def handle_subscription_cancelled
    payload = params[:payload]
    merchant_subscription_id = payload.dig(:merchantSubscriptionId)

    # Find the subscription by pg_id
    subscription = Subscription.find_by(pg_id: merchant_subscription_id)

    if subscription.present?
      # Update subscription with payload
      subscription.pg_json = payload
      subscription.save!

      # Cancel the subscription if it's in active, on_hold, or paused state
      if subscription.may_customer_cancel?
        subscription.customer_cancel!
        Rails.logger.warn("PhonePe subscription cancelled for subscription ID: #{subscription.id}")
      elsif !subscription.cancelled?
        Honeybadger.notify('Invalid state for subscription in PhonePe subscription cancelled',
                           context: { subscription_id: subscription.id, current_state: subscription.status, params: params })
      end
    else
      Rails.logger.error("PhonePe subscription not found for merchant_subscription_id: #{merchant_subscription_id}")
      Honeybadger.notify('PhonePe subscription not found for cancellation',
                         context: { merchant_subscription_id: merchant_subscription_id, params: params })
    end
  end

  def handle_subscription_revoked
    payload = params[:payload]
    merchant_subscription_id = payload.dig(:merchantSubscriptionId)

    # Find the subscription by pg_id
    subscription = Subscription.find_by(pg_id: merchant_subscription_id)

    if subscription.present?
      # Update subscription with payload
      subscription.pg_json = payload
      subscription.save!

      # Cancel the subscription if it's in active, on_hold, or paused state
      if subscription.may_customer_cancel?
        subscription.customer_cancel!
        Rails.logger.warn("PhonePe subscription revoked for subscription ID: #{subscription.id}")
      elsif !subscription.cancelled?
        Honeybadger.notify('Invalid state for subscription in PhonePe subscription revoked',
                           context: { subscription_id: subscription.id, current_state: subscription.status, params: params })
      end
    else
      Rails.logger.error("PhonePe subscription not found for merchant_subscription_id: #{merchant_subscription_id}")
      Honeybadger.notify('PhonePe subscription not found for revocation',
                         context: { merchant_subscription_id: merchant_subscription_id, params: params })
    end
  end

  def handle_subscription_paused
    payload = params[:payload]
    merchant_subscription_id = payload.dig(:merchantSubscriptionId)

    # Find the subscription by pg_id
    subscription = Subscription.find_by(pg_id: merchant_subscription_id)

    if subscription.present?
      # Update subscription with payload
      subscription.pg_json = payload
      subscription.save!

      # Pause the subscription if it's in active or on_hold state
      if subscription.may_customer_pause?
        subscription.customer_pause!
        Rails.logger.warn("PhonePe subscription paused for subscription ID: #{subscription.id}")
      elsif !subscription.cancelled?
        Honeybadger.notify('Invalid state for subscription in PhonePe subscription paused',
                           context: { subscription_id: subscription.id, current_state: subscription.status, params: params })
      end
    else
      Rails.logger.error("PhonePe subscription not found for merchant_subscription_id: #{merchant_subscription_id}")
      Honeybadger.notify('PhonePe subscription not found for pause',
                         context: { merchant_subscription_id: merchant_subscription_id, params: params })
    end
  end

  def handle_refund_callback
    # Handle refund completion or failure events
    payload = params[:payload]
    merchant_refund_id = payload[:merchantRefundId]
    original_merchant_order_id = payload[:originalMerchantOrderId]
    refund_state = payload[:state]

    Rails.logger.warn("PhonePe refund callback received: event=#{params[:event]}, merchantRefundId=#{merchant_refund_id}, state=#{refund_state}")

    # Extract refund ID from merchant_refund_id (format: "refund-{refund_id}-{subscription_charge_pg_id}")
    if merchant_refund_id.present? && merchant_refund_id.start_with?('refund-')
      refund_id_match = merchant_refund_id.match(/^refund-(\d+)-/)
      refund_id = refund_id_match[1] if refund_id_match

      if refund_id.present?
        refund = SubscriptionChargeRefund.find_by(id: refund_id)

        if refund.present?
          # Update the refund record with the callback payload
          refund.update(pg_json: payload)

          # Update refund status based on the event and state
          if params[:event] == "pg.refund.completed" && refund_state == "COMPLETED"
            if refund.may_mark_as_success?
              refund.mark_as_success!
              Rails.logger.warn("PhonePe refund marked as success for refund ID: #{refund.id}")
            else
              Honeybadger.notify('Invalid state for refund in PhonePe refund completed',
                                 context: { refund_id: refund.id, current_state: refund.status, params: params })
            end
          elsif params[:event] == "pg.refund.failed" && refund_state == "FAILED"
            if refund.may_mark_as_failed?
              refund.mark_as_failed!
              Rails.logger.warn("PhonePe refund marked as failed for refund ID: #{refund.id}")
            else
              Honeybadger.notify('Invalid state for refund in PhonePe refund failed',
                                 context: { refund_id: refund.id, current_state: refund.status, params: params })
            end
          else
            Rails.logger.warn("Unhandled PhonePe refund state: #{refund_state} for event: #{params[:event]}")
          end
        else
          Rails.logger.error("PhonePe refund not found for refund_id: #{refund_id}")
          Honeybadger.notify('PhonePe refund not found for callback',
                             context: { refund_id: refund_id, merchant_refund_id: merchant_refund_id, params: params })
        end
      else
        Rails.logger.error("Could not extract refund_id from merchant_refund_id: #{merchant_refund_id}")
        Honeybadger.notify('Invalid merchant_refund_id format in PhonePe refund callback',
                           context: { merchant_refund_id: merchant_refund_id, params: params })
      end
    else
      Rails.logger.error("Invalid merchant_refund_id format: #{merchant_refund_id}")
      Honeybadger.notify('Invalid merchant_refund_id in PhonePe refund callback',
                         context: { merchant_refund_id: merchant_refund_id, params: params })
    end
  end
end
